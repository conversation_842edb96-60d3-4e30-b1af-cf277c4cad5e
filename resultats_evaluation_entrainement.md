# Résultats et évaluation de l'entraînement

## Métriques d'évaluation : Définitions et formules

Avant d'analyser les résultats obtenus, il est essentiel de définir les métriques d'évaluation utilisées pour évaluer les performances du modèle YOLOv8n sur le dataset KITTI modifié.

### Définitions des termes fondamentaux

- **Vrais Positifs (TP)** : Objets correctement détectés et classifiés
- **Faux Positifs (FP)** : Détections incorrectes (objets détectés qui n'existent pas ou mal classifiés)
- **Vrais Négatifs (TN)** : Absence d'objets correctement identifiée
- **Faux Négatifs (FN)** : Objets présents mais non détectés par le modèle

### Métriques de performance

#### Précision (Precision)
La précision mesure la proportion de détections correctes parmi toutes les détections effectuées par le modèle.

**Formule :**
```
Précision = TP / (TP + FP)
```

Une précision élevée indique que le modèle génère peu de fausses alarmes [1].

#### Rappel (Recall)
Le rappel, également appelé sensibilité, mesure la proportion d'objets réels correctement détectés par le modèle.

**Formule :**
```
Rappel = TP / (TP + FN)
```

Un rappel élevé signifie que le modèle détecte la plupart des objets présents dans l'image [1].

#### Score F1
Le score F1 est la moyenne harmonique de la précision et du rappel, offrant un équilibre entre ces deux métriques.

**Formule :**
```
F1 = 2 × (Précision × Rappel) / (Précision + Rappel)
```

#### Confiance (Confidence)
La confiance représente la probabilité estimée par le modèle qu'une détection soit correcte. Elle varie entre 0 et 1 [2].

## Analyse de la matrice de confusion

La matrice de confusion normalisée révèle les performances de classification pour chaque classe d'objets du dataset KITTI modifié.

### Performance par classe

#### Classes avec excellentes performances :
- **Tram** : 96% de précision de classification, avec seulement 1% de confusion avec la classe "DontCare"
- **Car** : 92% de précision, avec 14% de confusion avec "Van" (confusion compréhensible due à la similarité morphologique)
- **Truck** : 88% de précision, performance robuste avec peu de confusions

#### Classes avec bonnes performances :
- **Van** : 75% de précision, avec 5% de confusion avec "Truck" et "DontCare"
- **Person_sitting** : 76% de précision, performance satisfaisante pour cette classe spécifique
- **Pedestrian** : 72% de précision, avec 6% de confusion avec "Person_sitting" et 9% avec "background"

#### Classes avec performances modérées :
- **Cyclist** : 67% de précision, avec 3% de confusion avec "Tram" et "DontCare"
- **Misc** : 64% de précision, classe hétérogène par nature

#### Classe problématique :
- **DontCare** : 52% de précision avec 46% de confusion avec "background", reflétant la nature ambiguë de cette classe d'annotation

### Analyse des confusions inter-classes

Les principales confusions observées sont :
1. **Car ↔ Van** (14%) : Confusion logique due à la similarité des véhicules
2. **DontCare ↔ Background** (46%) : Difficulté à distinguer les objets non pertinents du fond
3. **Pedestrian ↔ Background** (9%) : Défis de détection des piétons dans des conditions extrêmes

## Analyse des courbes de performance

### Courbe F1-Confiance

La courbe F1-confiance montre l'évolution du score F1 en fonction du seuil de confiance :

- **Performance globale** : F1 = 0.69 à un seuil de confiance de 0.377
- **Meilleures classes** : 
  - Truck (vert) : F1 ≈ 0.89, très stable sur une large plage de confiance
  - Car (bleu) : F1 ≈ 0.87, performance constante
  - Van (orange) : F1 ≈ 0.81, bon équilibre précision-rappel

- **Classes déficitaires** :
  - DontCare (jaune) : F1 ≈ 0.32, chute rapide avec l'augmentation de la confiance
  - Pedestrian (rouge) : F1 ≈ 0.62, performance variable selon le seuil

### Courbe Rappel-Confiance

L'analyse du rappel en fonction de la confiance révèle :

- **Rappel global** : 0.81 à confiance 0.000 (seuil minimal)
- **Dégradation progressive** : Le rappel diminue avec l'augmentation du seuil de confiance
- **Classes robustes** : Truck, Car, et Van maintiennent un rappel élevé même à haute confiance
- **Classes sensibles** : DontCare et Pedestrian montrent une chute rapide du rappel

### Courbe Précision-Confiance

La courbe précision-confiance illustre :

- **Précision globale** : 1.00 à confiance 1.000 (seuil maximal)
- **Amélioration avec la confiance** : La précision augmente généralement avec le seuil
- **Stabilité des classes** : La plupart des classes atteignent une précision > 0.95 à haute confiance
- **Exception notable** : DontCare présente une précision instable et faible

## Évaluation des performances en conditions extrêmes

Les résultats obtenus démontrent que le modèle YOLOv8n entraîné sur le dataset KITTI modifié présente :

### Points forts :
1. **Détection véhiculaire excellente** : Cars, Vans, et Trucks bien détectés
2. **Robustesse aux variations** : Performance stable sur différents seuils de confiance
3. **Équilibre précision-rappel** : Score F1 global de 0.69 acceptable pour des conditions extrêmes

### Points d'amélioration :
1. **Détection piétonne** : Nécessite optimisation pour les conditions difficiles
2. **Gestion classe DontCare** : Redéfinition ou suppression à considérer
3. **Seuil optimal** : Ajustement du seuil de confiance selon l'application

## Références

[1] Sokolova, M., & Lapalme, G. (2009). A systematic analysis of performance measures for classification tasks. Information processing & management, 45(4), 427-437.

[2] Redmon, J., & Farhadi, A. (2018). YOLOv3: An incremental improvement. arXiv preprint arXiv:1804.02767.
